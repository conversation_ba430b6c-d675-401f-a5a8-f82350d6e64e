import os
import json
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from torchvision.models import resnet50, efficientnet_b0
import timm
from PIL import Image
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# ========================= 数据加载和预处理 =========================

class StreetViewEnergyDataset(Dataset):
    """街景图像能耗预测数据集"""
    
    def __init__(self, region_info_path, image_mapping_path, region_ids_path, 
                 image_dir, transform=None, max_images_per_region=10):
        """
        Args:
            region_info_path: 区域信息JSON文件路径
            image_mapping_path: 图像映射JSON文件路径  
            region_ids_path: 区域ID CSV文件路径
            image_dir: 图像目录路径
            transform: 图像变换
            max_images_per_region: 每个区域最大图像数量
        """
        
        # 加载区域信息
        with open(region_info_path, 'r', encoding='utf-8') as f:
            self.region_info = json.load(f)
            
        # 加载图像映射
        with open(image_mapping_path, 'r', encoding='utf-8') as f:
            self.image_mapping = json.load(f)
            
        # 加载区域ID列表
        region_ids_df = pd.read_csv(region_ids_path)
        self.region_ids = region_ids_df.iloc[:, 0].astype(str).tolist()
        
        self.image_dir = image_dir
        self.transform = transform
        self.max_images = max_images_per_region
        
        # 准备数据
        self._prepare_data()
        
    def _prepare_data(self):
        """准备训练数据"""
        self.data = []
        
        for region_id in self.region_ids:
            if region_id in self.region_info and region_id in self.image_mapping:
                region_data = self.region_info[region_id]
                image_files = self.image_mapping[region_id]
                
                # 限制图像数量
                if len(image_files) > self.max_images:
                    image_files = image_files[:self.max_images]
                
                # 提取数值特征
                features = [
                    region_data.get('n_pois', 0),
                    region_data.get('pop', 0),
                    region_data.get('edu', 0),
                    region_data.get('income', 0),
                    region_data.get('cd', 0),
                    region_data.get('crime', 0)
                ]
                
                self.data.append({
                    'region_id': region_id,
                    'features': features,
                    'images': image_files,
                    'energy': region_data['energy']
                })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # 加载图像
        images = []
        for img_file in item['images']:
            img_path = os.path.join(self.image_dir, f"Region/{item['region_id']}", img_file)
            if os.path.exists(img_path):
                try:
                    image = Image.open(img_path).convert('RGB')
                    if self.transform:
                        image = self.transform(image)
                    images.append(image)
                except:
                    # 如果图像加载失败，创建默认图像
                    if self.transform:
                        default_img = torch.zeros(3, 224, 224)
                        images.append(default_img)
        
        # 确保至少有一张图像
        if not images:
            default_img = torch.zeros(3, 224, 224) if self.transform else Image.new('RGB', (224, 224))
            images.append(default_img)
        
        # 填充或截断到固定数量
        while len(images) < self.max_images:
            images.append(images[0])  # 重复第一张图像
        images = images[:self.max_images]
        
        return {
            'images': torch.stack(images),
            'features': torch.tensor(item['features'], dtype=torch.float32),
            'energy': torch.tensor(item['energy'], dtype=torch.float32)
        }

# ========================= 模型定义 =========================

class ResNetEnergyPredictor(nn.Module):
    """
    基于ResNet的能耗预测模型
    论文: "Deep Residual Learning for Image Recognition" (He et al., 2016)
    """
    
    def __init__(self, num_features=6, num_images=10, pretrained=True):
        super(ResNetEnergyPredictor, self).__init__()
        
        # 图像特征提取器
        self.resnet = resnet50(pretrained=pretrained)
        self.resnet.fc = nn.Identity()  # 移除最后的分类层
        
        # 图像特征聚合
        self.image_aggregator = nn.Sequential(
            nn.Linear(2048 * num_images, 512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 数值特征处理
        self.feature_processor = nn.Sequential(
            nn.Linear(num_features, 64),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 融合和预测
        self.predictor = nn.Sequential(
            nn.Linear(512 + 64, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1)
        )
        
    def forward(self, images, features):
        batch_size = images.shape[0]
        num_images = images.shape[1]
        
        # 重塑图像维度 (batch_size * num_images, C, H, W)
        images = images.view(-1, images.shape[2], images.shape[3], images.shape[4])
        
        # 提取图像特征
        with torch.no_grad():
            image_features = self.resnet(images)  # (batch_size * num_images, 2048)
        
        # 重塑回 (batch_size, num_images * 2048)
        image_features = image_features.view(batch_size, -1)
        
        # 聚合图像特征
        image_features = self.image_aggregator(image_features)
        
        # 处理数值特征
        numeric_features = self.feature_processor(features)
        
        # 特征融合
        combined_features = torch.cat([image_features, numeric_features], dim=1)
        
        # 预测
        energy = self.predictor(combined_features)
        
        return energy.squeeze()

class ViTEnergyPredictor(nn.Module):
    """
    基于Vision Transformer的能耗预测模型
    论文: "An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale" (Dosovitskiy et al., 2021)
    """
    
    def __init__(self, num_features=6, num_images=10):
        super(ViTEnergyPredictor, self).__init__()
        
        # 使用预训练的ViT
        self.vit = timm.create_model('vit_base_patch16_224', pretrained=True, num_classes=0)
        
        # 图像特征聚合
        self.image_aggregator = nn.Sequential(
            nn.Linear(768 * num_images, 512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 数值特征处理
        self.feature_processor = nn.Sequential(
            nn.Linear(num_features, 64),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 融合和预测
        self.predictor = nn.Sequential(
            nn.Linear(512 + 64, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1)
        )
        
    def forward(self, images, features):
        batch_size = images.shape[0]
        num_images = images.shape[1]
        
        # 重塑图像维度
        images = images.view(-1, images.shape[2], images.shape[3], images.shape[4])
        
        # 提取图像特征
        image_features = self.vit(images)  # (batch_size * num_images, 768)
        
        # 重塑回 (batch_size, num_images * 768)
        image_features = image_features.view(batch_size, -1)
        
        # 聚合图像特征
        image_features = self.image_aggregator(image_features)
        
        # 处理数值特征
        numeric_features = self.feature_processor(features)
        
        # 特征融合
        combined_features = torch.cat([image_features, numeric_features], dim=1)
        
        # 预测
        energy = self.predictor(combined_features)
        
        return energy.squeeze()

class EfficientNetEnergyPredictor(nn.Module):
    """
    基于EfficientNet的能耗预测模型
    论文: "EfficientNet: Rethinking Model Scaling for Convolutional Neural Networks" (Tan & Le, 2019)
    """
    
    def __init__(self, num_features=6, num_images=10, pretrained=True):
        super(EfficientNetEnergyPredictor, self).__init__()
        
        # 图像特征提取器
        self.efficientnet = efficientnet_b0(pretrained=pretrained)
        self.efficientnet.classifier = nn.Identity()
        
        # 图像特征聚合
        self.image_aggregator = nn.Sequential(
            nn.Linear(1280 * num_images, 512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 数值特征处理
        self.feature_processor = nn.Sequential(
            nn.Linear(num_features, 64),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 融合和预测
        self.predictor = nn.Sequential(
            nn.Linear(512 + 64, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1)
        )
        
    def forward(self, images, features):
        batch_size = images.shape[0]
        num_images = images.shape[1]
        
        # 重塑图像维度
        images = images.view(-1, images.shape[2], images.shape[3], images.shape[4])
        
        # 提取图像特征
        image_features = self.efficientnet(images)  # (batch_size * num_images, 1280)
        
        # 重塑回 (batch_size, num_images * 1280)
        image_features = image_features.view(batch_size, -1)
        
        # 聚合图像特征
        image_features = self.image_aggregator(image_features)
        
        # 处理数值特征
        numeric_features = self.feature_processor(features)
        
        # 特征融合
        combined_features = torch.cat([image_features, numeric_features], dim=1)
        
        # 预测
        energy = self.predictor(combined_features)
        
        return energy.squeeze()

class AttentionCNNEnergyPredictor(nn.Module):
    """
    基于注意力机制的CNN能耗预测模型
    结合了注意力机制，参考多篇相关论文的设计
    """
    
    def __init__(self, num_features=6, num_images=10, pretrained=True):
        super(AttentionCNNEnergyPredictor, self).__init__()
        
        # 图像特征提取器
        self.backbone = resnet50(pretrained=pretrained)
        self.backbone.fc = nn.Identity()
        
        # 注意力机制
        self.attention = nn.Sequential(
            nn.Linear(2048, 512),
            nn.ReLU(),
            nn.Linear(512, 1),
            nn.Softmax(dim=1)
        )
        
        # 数值特征处理
        self.feature_processor = nn.Sequential(
            nn.Linear(num_features, 64),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 融合和预测
        self.predictor = nn.Sequential(
            nn.Linear(2048 + 64, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 1)
        )
        
    def forward(self, images, features):
        batch_size = images.shape[0]
        num_images = images.shape[1]
        
        # 重塑图像维度
        images = images.view(-1, images.shape[2], images.shape[3], images.shape[4])
        
        # 提取图像特征
        image_features = self.backbone(images)  # (batch_size * num_images, 2048)
        
        # 重塑回 (batch_size, num_images, 2048)
        image_features = image_features.view(batch_size, num_images, -1)
        
        # 计算注意力权重
        attention_weights = self.attention(image_features)  # (batch_size, num_images, 1)
        
        # 加权聚合图像特征
        weighted_features = torch.sum(image_features * attention_weights, dim=1)  # (batch_size, 2048)
        
        # 处理数值特征
        numeric_features = self.feature_processor(features)
        
        # 特征融合
        combined_features = torch.cat([weighted_features, numeric_features], dim=1)
        
        # 预测
        energy = self.predictor(combined_features)
        
        return energy.squeeze()

# ========================= 训练和评估函数 =========================

def train_model(model, train_loader, val_loader, num_epochs=50, learning_rate=0.001, device='cuda'):
    """训练模型"""
    model.to(device)
    
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
    
    train_losses = []
    val_losses = []
    
    best_val_loss = float('inf')
    best_model_state = None
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch in train_loader:
            images = batch['images'].to(device)
            features = batch['features'].to(device)
            targets = batch['energy'].to(device)
            
            optimizer.zero_grad()
            outputs = model(images, features)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['images'].to(device)
                features = batch['features'].to(device)
                targets = batch['energy'].to(device)
                
                outputs = model(images, features)
                loss = criterion(outputs, targets)
                val_loss += loss.item()
        
        avg_val_loss = val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            best_model_state = model.state_dict().copy()
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        if epoch % 10 == 0:
            print(f'Epoch {epoch}/{num_epochs}, Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}')
    
    # 加载最佳模型
    model.load_state_dict(best_model_state)
    
    return model, train_losses, val_losses

def evaluate_model(model, test_loader, device='cuda'):
    """评估模型"""
    model.to(device)
    model.eval()
    
    predictions = []
    targets = []
    
    with torch.no_grad():
        for batch in test_loader:
            images = batch['images'].to(device)
            features = batch['features'].to(device)
            target = batch['energy'].to(device)
            
            output = model(images, features)
            
            predictions.extend(output.cpu().numpy())
            targets.extend(target.cpu().numpy())
    
    predictions = np.array(predictions)
    targets = np.array(targets)
    
    # 计算评估指标
    mse = mean_squared_error(targets, predictions)
    mae = mean_absolute_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    rmse = np.sqrt(mse)
    
    return {
        'MSE': mse,
        'MAE': mae,
        'RMSE': rmse,
        'R2': r2,
        'predictions': predictions,
        'targets': targets
    }

# ========================= 主函数 =========================

def main():
    """主函数"""
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 数据路径配置（需要根据实际路径修改）
    base_path = "./data/shenyang"
    region_info_path = os.path.join(base_path, "shenyang_region2allinfo.json")
    image_mapping_path = os.path.join(base_path, "streetview_image/region_5_10_poi_image_filename.json")
    train_ids_path = os.path.join(base_path, "shenyang_zl15_train.csv")
    val_ids_path = os.path.join(base_path, "shenyang_zl15_valid.csv")
    test_ids_path = os.path.join(base_path, "shenyang_zl15_test.csv")
    image_dir = os.path.join(base_path, "streetview_image")
    
    # 数据变换
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 创建数据集
    print("Loading datasets...")
    train_dataset = StreetViewEnergyDataset(
        region_info_path, image_mapping_path, train_ids_path, 
        image_dir, transform, max_images_per_region=10
    )
    
    val_dataset = StreetViewEnergyDataset(
        region_info_path, image_mapping_path, val_ids_path,
        image_dir, transform, max_images_per_region=10
    )
    
    test_dataset = StreetViewEnergyDataset(
        region_info_path, image_mapping_path, test_ids_path,
        image_dir, transform, max_images_per_region=10
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False, num_workers=2)
    
    print(f"Train samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    print(f"Test samples: {len(test_dataset)}")
    
    # 定义模型
    models = {
        'ResNet50': ResNetEnergyPredictor(num_features=6, num_images=10),
        'ViT': ViTEnergyPredictor(num_features=6, num_images=10),
        'EfficientNet': EfficientNetEnergyPredictor(num_features=6, num_images=10),
        'AttentionCNN': AttentionCNNEnergyPredictor(num_features=6, num_images=10)
    }
    
    results = {}
    
    # 训练和评估每个模型
    for model_name, model in models.items():
        print(f"\n{'='*50}")
        print(f"Training {model_name}...")
        print(f"{'='*50}")
        
        # 训练模型
        trained_model, train_losses, val_losses = train_model(
            model, train_loader, val_loader, 
            num_epochs=30, learning_rate=0.001, device=device
        )
        
        # 评估模型
        print(f"Evaluating {model_name}...")
        result = evaluate_model(trained_model, test_loader, device)
        results[model_name] = result
        
        print(f"{model_name} Results:")
        print(f"  MSE: {result['MSE']:.4f}")
        print(f"  MAE: {result['MAE']:.4f}")
        print(f"  RMSE: {result['RMSE']:.4f}")
        print(f"  R²: {result['R2']:.4f}")
    
    # 结果比较
    print(f"\n{'='*50}")
    print("Model Comparison")
    print(f"{'='*50}")
    
    comparison_df = pd.DataFrame({
        model_name: {
            'MSE': result['MSE'],
            'MAE': result['MAE'], 
            'RMSE': result['RMSE'],
            'R²': result['R2']
        }
        for model_name, result in results.items()
    }).T
    
    print(comparison_df)
    
    # 可视化结果
    plt.figure(figsize=(15, 10))
    
    # 评估指标比较
    plt.subplot(2, 3, 1)
    comparison_df['R²'].plot(kind='bar')
    plt.title('R² Score Comparison')
    plt.ylabel('R² Score')
    plt.xticks(rotation=45)
    
    plt.subplot(2, 3, 2)
    comparison_df['RMSE'].plot(kind='bar')
    plt.title('RMSE Comparison')
    plt.ylabel('RMSE')
    plt.xticks(rotation=45)
    
    plt.subplot(2, 3, 3)
    comparison_df['MAE'].plot(kind='bar')
    plt.title('MAE Comparison')
    plt.ylabel('MAE')
    plt.xticks(rotation=45)
    
    # 预测vs真实值散点图（选择最佳模型）
    best_model = max(results.keys(), key=lambda x: results[x]['R2'])
    best_result = results[best_model]
    
    plt.subplot(2, 3, 4)
    plt.scatter(best_result['targets'], best_result['predictions'], alpha=0.6)
    plt.plot([best_result['targets'].min(), best_result['targets'].max()], 
             [best_result['targets'].min(), best_result['targets'].max()], 'r--', lw=2)
    plt.xlabel('True Energy')
    plt.ylabel('Predicted Energy')
    plt.title(f'{best_model} - Predictions vs True Values')
    
    # 残差图
    plt.subplot(2, 3, 5)
    residuals = best_result['targets'] - best_result['predictions']
    plt.scatter(best_result['predictions'], residuals, alpha=0.6)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('Predicted Energy')
    plt.ylabel('Residuals')
    plt.title(f'{best_model} - Residuals Plot')
    
    # 模型性能总结
    plt.subplot(2, 3, 6)
    metrics = ['R²', 'RMSE', 'MAE']
    best_scores = [best_result['R2'], best_result['RMSE'], best_result['MAE']]
    plt.bar(metrics, best_scores)
    plt.title(f'Best Model ({best_model}) Performance')
    plt.ylabel('Score')
    
    plt.tight_layout()
    plt.show()
    
    print(f"\nBest performing model: {best_model}")
    print(f"Best R² score: {best_result['R2']:.4f}")

if __name__ == "__main__":
    main()
