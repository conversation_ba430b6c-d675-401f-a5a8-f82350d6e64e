# 沈阳城市能耗预测数据集说明文档

## 📋 数据集概述

**数据集名称**: 沈阳城市街景能耗预测数据集
**数据集类型**: 多街景城市能耗预测
**任务类型**: 回归预测
**生成时间**: 2025-05-30 17:41:47

## 📁 数据文件结构

### 核心数据文件

| 文件类型 | 文件路径 | 格式 | 用途 |
|---------|----------|------|------|
| 区域详细信息和标签 | `D:\研二\能耗估算\666-模型对比项目\V6\data\shenyang\shenyang_region2allinfo.json` | JSON文件 | 提供区域的多维特征和能耗标签 |
| POI街景图像文件名映射 | `./data/shenyang/streetview_image/region_5_10_poi_image_filename.json` | JSON文件 | 建立区域与街景图像文件的对应关系 |
| 训练集区域ID | `./data/shenyang/shenyang_zl15_train.csv` | CSV文件 | 指定用于模型训练的区域 |
| 验证集区域ID | `./data/shenyang/shenyang_zl15_valid.csv` | CSV文件 | 模型验证和超参数调优 |
| 测试集区域ID | `./data/shenyang/shenyang_zl15_test.csv` | CSV文件 | 最终模型性能评估 |
| 街景图像 | `./data/shenyang/streetview_image/Region/` | 目录结构的JPG/PNG文件 | 提供区域的街道级视觉特征 |

## 📊 数据集统计

| 统计项 | 数值 |
|--------|------|
| 总区域数量 | 122 |
| 训练区域数 | 97 |
| 验证区域数 | 13 |
| 测试区域数 | 14 |
| 区域ID范围 | [150, 719] |
| POI图像映射 | 122 区域, 4880 张图像 |

## 🔄 数据使用流程

1. 读取区域信息 (region2allinfo.json) 获取特征和能耗标签
2. 根据数据集划分文件 (train/val/test.csv) 分配训练数据
3. 加载街景图像特征
4. 训练网络进行能耗预测

## 📋 数据字段说明

### 区域信息字段 (region2allinfo.json)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| energy | float | **能耗标签** - 目标预测变量 |
| n_pois | int | POI兴趣点数量 |
| pop | float | 人口数量 |
| edu | float | 教育水平指标 |
| income | float | 收入水平指标 |
| center | list | 区域中心坐标 [经度, 纬度] |
| cd | float | 建筑密度 |
| crime | float | 犯罪率 |
| *_cate | int | 对应字段的分类编码 |

{"150": {"center": [123.44289073986096, 41.74860564278515], "n_pois": 12, "pop": 0, "edu": 0, "income": 0, "unemploy": 0, "pop_cate": "pop_1", "edu_cate": "edu_1", "income_cate": "income_1", "unemploy_cate": "unemploy_1", "cd": 0, "crime": 0, "crime_cate": "crime_1", "margin": [[123.44287473826022, 41.74378954668751], [123.44286735936646, 41.74378513805352], [123.44286013087391, 41.74378978918485], [123.44252743316382, 41.744050285847166], [123.44247300289027, 41.74409287485312], [123.4413794841766, 41.744952893473595], [123.44137745905252, 41.744954529762474], [123.44127185501465, 41.745042179183244], [123.44127063442345, 41.745043184042544], [123.44126918720455, 41.745044365804866], [123.44126795862307, 41.745045360878066], [123.43890217140519, 41.74694589599529], [123.43888373990676, 41.74696539130356], [123.43841030308555, 41.74762758338612], [123.43839934164916, 41.74764737976533], [123.43817113506014, 41.74820147914995], [123.43816632555206, 41.74821643972071], [123.43805431671902, 41.74869180341128], [123.43805309347049, 41.7487013039414], [123.43806062957519, 41.748707216938584], [123.43808813771703, 41.74872453752805], [123.43810853165148, 41.748739268791454], [123.4381313528715, 41.748758072550665], [123.4381497125507, 41.7487752726921], [123.4382194855069, 41.748849515090384], [123.43823070861504, 41.74886254880603], [123.43824325818744, 41.74887847362227], [123.43825330688868, 41.74889243283088], [123.43829897739526, 41.7489620722559], [123.43830994655275, 41.748981004470345], [123.4383166139603, 41.74899413652188], [123.43833197172013, 41.7490350423724], [123.43833288259418, 41.74903863193334], [123.43833888733941, 41.74908191117146], [123.43833928813757, 41.74909663341544], [123.43833867324508, 41.74911850515679], [123.4383332745872, 41.74918306410628], [123.43833036195396, 41.74920415159918], [123.43832763880769, 41.749218229349104], [123.43831512701612, 41.749258860274516], [123.43830945924934, 41.74927203126486], [123.43830000493546, 41.74929110432811], [123.43824106566316, 41.749395487431876], [123.43806933235308, 41.74969953658903], [123.43806651591233, 41.749704213429396], [123.43780561921191, 41.750152009363696], [123.4376650939682, 41.75039719360638], [123.43765462621708, 41.75041354220346], [123.43764702670512, 41.75042421885932], [123.43762154650753, 41.7504534605244], [123.43761911728014, 41.75045575028952], [123.43758842191113, 41.750479459144586], [123.4375855926495, 41.750481230993806], [123.4375508617159, 41.75049849592007], [123.43754819245679, 41.75049951026079], [123.43753977373746, 41.75050369744444], [123.43753511033925, 41.750511862005574], [123.43740153479965, 41.75081051612943], [123.43740096489447, 41.750811778474905], [123.43691001437094, 41.751889167208724], [123.43690629325613, 41.75189688570895], [123.43690452875613, 41.751900351774616], [123.43690047721988, 41.751907901405815], [123.43689723223551, 41.751913645991976], [123.43689375401237, 41.75192146545635], [123.43690142715475, 41.751925255567976], [123.43698484806431, 41.75195695420916], [123.43699544670805, 41.75196133714427], [123.43700019370927, 41.751963463348915], [123.43701052057865, 41.75196845305158], [123.43827696572005, 41.75262630922178], [123.438279810771, 41.752627729492175], [123.43887284648105, 41.752911963244244], [123.43888467186233, 41.75291671946228], [123.4391517356948, 41.75300459090684], [123.43916184779077, 41.75300733354953], [123.43946308206421, 41.7530721171212], [123.43947035717183, 41.753073399481664], [123.43960546663082, 41.753092042035895], [123.43961434016748, 41.753092445573856], [123.43962276849085, 41.75308964095978], [123.45143259320102, 41.74791588135396], [123.45143904651225, 41.74791227042202], [123.45143197697644, 41.74791010115509], [123.45107171396343, 41.747836889085285], [123.45105811896016, 41.74783362218074], [123.45046767339039, 41.7476694659908], [123.45046385185415, 41.74766836202035], [123.45046212039695, 41.7476678429207], [123.45045832182838, 41.74766666234537], [123.44878237825024, 41.747127254142804], [123.44877569289602, 41.74712496993194], [123.44877267571982, 41.74712387850566], [123.44876607617863, 41.747121357069645], [123.4475637281717, 41.74663721213641], [123.44755959811825, 41.74663549459671], [123.44755773041048, 41.74663469300258], [123.44755364023845, 41.74663288253611], [123.44683624001726, 41.746305586131754], [123.44683503243176, 41.74630503029127], [123.44640805412021, 41.746106755078166], [123.446398079866, 41.74610177710239], [123.4463936212353, 41.746099392684194], [123.44638394317508, 41.746093860855254], [123.44430356966632, 41.74482569273929], [123.44429731335033, 41.74482171632726], [123.44429450659273, 41.744819857672276], [123.44428840362927, 41.74481564968099], [123.44376431498914, 41.744439612606385], [123.4437629207779, 41.74443860303427], [123.44287473826022, 41.74378954668751]], "energy": 1.6599758660205153, "energy_cate": "energy_2", "cate1_poi_number": [1, 0, 0, 0, 4, 0, 4, 0, 0, 0, 2, 1, 0, 0, 0, 0, 0, 0, 0]}, "152": {"center": [123.44021160935434, 41.75612539270175], "n_pois": 4, "pop": 0, "edu": 0, "income": 0, "unemploy": 0, "pop_cate": "pop_1", "edu_cate": "edu_1", "income_cate": "income_1", "unemploy_cate": "unemploy_1", "cd": 0, "crime": 0, "crime_cate": "crime_1", "margin": [[123.43900042649398, 41.754353788911615], [123.43880591394836, 41.75434888793462], [123.438788099051, 41.754350053854864], [123.43861898009135, 41.7543766347141], [123.4385930279145, 41.75438449373836], [123.43745552256857, 41.754909209297765], [123.43744780856653, 41.75491377451607], [123.43745285786706, 41.75492118070318], [123.43778057593998, 41.75530981946982], [123.43778476514304, 41.75531447948492], [123.43867035471929, 41.756238935061376], [123.43867336123415, 41.7562419414274], [123.43893595241343, 41.756493467824654], [123.43919422392791, 41.756741018991164], [123.43919849882266, 41.756744883873154], [123.43962015991227, 41.75710434067507], [123.43962562195782, 41.757108673989315], [123.44032119407755, 41.757621617596065], [123.44032369291904, 41.75762349097178], [123.44032391857303, 41.757623662942734], [123.44032638753015, 41.75762557552654], [123.44106307170071, 41.758205610681415], [123.44107173973572, 41.75821106683863], [123.44108135605448, 41.75821459258605], [123.4422147430438, 41.75849945354442], [123.44222340379494, 41.75850070198202], [123.44222217102106, 41.75849203898737], [123.44211642071883, 41.75806804445946], [123.44211596674191, 41.75806618606999], [123.44202892071303, 41.75770223269533], [123.44202681599155, 41.75769231596176], [123.4420266493653, 41.75769141820085], [123.442025055709, 41.757681409289546], [123.44198092870792, 41.75734977640916], [123.44197993086202, 41.75734066880586], [123.44197985978481, 41.75733984408068], [123.44197928465226, 41.757330700043255], [123.44194488997867, 41.75646387474512], [123.44190572189878, 41.755565249234955], [123.44190586738311, 41.755545156884], [123.44190597019748, 41.7555433874464], [123.44190815284702, 41.755523413134014], [123.44194422701912, 41.755299366264225], [123.44194477226856, 41.75529018093594], [123.44193633590577, 41.75528635378381], [123.44193357216122, 41.755285392719856], [123.44192955493833, 41.75528395454829], [123.44192616661599, 41.755282706526366], [123.44131928602415, 41.75505286329319], [123.44131406228148, 41.7550510501896], [123.43920481090287, 41.75438440581444], [123.43918942253049, 41.75438086597786], [123.43901232327684, 41.754354810180025], [123.43900042649398, 41.754353788911615]], "energy": 0.7070861177883299, "energy_cate": "energy_1", "cate1_poi_number": [0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0]}


### POI图像映射 (region_5_10_poi_image_filename.json)

**映射统计**: 122 个区域, 4,880 张街景图像

**文件结构**:
JSON格式示例:
```json
{
  "150": ["123.43435_41.7520806_0_0.jpg", "123.43476_41.7522333_270_0.jpg"],
  "152": ["123.4357362_41.7562567_90_0.jpg", "123.435737_41.7569724_270_0.jpg"]
}
```

**图像分布**:
- 平均每区域: 40.0 张图像
- 图像数量范围: [40, 40]
